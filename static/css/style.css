/* Chess AI Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.game-container {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Controls Panel */
.controls-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.player-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.player {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.player i {
    font-size: 1.5rem;
}

.human-player i {
    color: #007bff;
}

.ai-player i {
    color: #dc3545;
}

.vs {
    font-weight: bold;
    font-size: 1.2rem;
    color: #666;
}

.game-status {
    text-align: center;
    margin-bottom: 20px;
}

.turn-indicator {
    padding: 10px;
    background: #e9ecef;
    border-radius: 5px;
    font-weight: bold;
}

.game-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 5px;
    font-weight: bold;
}

.game-result.win {
    background: #d4edda;
    color: #155724;
}

.game-result.lose {
    background: #f8d7da;
    color: #721c24;
}

.game-result.draw {
    background: #fff3cd;
    color: #856404;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-success {
    background: #28a745;
    color: white;
}

.difficulty-selector {
    margin-top: 20px;
}

.difficulty-selector label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

/* Chess Board */
.board-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.chess-board {
    width: 480px;
    height: 480px;
    border: 3px solid #8B4513;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.square {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.square.light {
    background: #f0d9b5;
}

.square.dark {
    background: #b58863;
}

.square:hover {
    box-shadow: inset 0 0 0 3px #007bff;
}

.square.selected {
    box-shadow: inset 0 0 0 3px #28a745 !important;
}

.square.legal-move {
    box-shadow: inset 0 0 0 2px #ffc107;
}

.square.last-move {
    background: rgba(255, 255, 0, 0.3) !important;
}

.move-input {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.input-group {
    display: flex;
    gap: 10px;
}

.input-group input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    text-align: center;
    text-transform: lowercase;
}

/* Side Panel */
.side-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.suggestions-panel,
.history-panel,
.info-panel {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.suggestions-panel h3,
.history-panel h3,
.info-panel h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestions-list {
    max-height: 200px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #007bff;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.suggestion-move {
    font-weight: bold;
    color: #007bff;
}

.suggestion-eval {
    font-size: 0.9rem;
    color: #666;
    margin-top: 2px;
}

.move-history {
    max-height: 200px;
    overflow-y: auto;
}

.history-item {
    padding: 5px 10px;
    margin-bottom: 5px;
    background: #f8f9fa;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9rem;
}

.history-item.human {
    border-left: 3px solid #007bff;
}

.history-item.ai {
    border-left: 3px solid #dc3545;
}

.game-info-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.label {
    font-weight: 500;
    color: #666;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.loading-spinner p {
    font-size: 1.2rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-content h3 {
    margin-bottom: 20px;
    text-align: center;
    color: #333;
}

.modal-body {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.radio-group label:hover {
    border-color: #007bff;
}

.radio-group input[type="radio"]:checked + i {
    transform: scale(1.2);
}

.modal-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .game-container {
        grid-template-columns: 250px 1fr 250px;
    }
    
    .chess-board {
        width: 400px;
        height: 400px;
    }
}

@media (max-width: 900px) {
    .game-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .chess-board {
        width: 320px;
        height: 320px;
    }
    
    .square {
        font-size: 2rem;
    }
}

@media (max-width: 600px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .chess-board {
        width: 280px;
        height: 280px;
    }
    
    .square {
        font-size: 1.5rem;
    }
}
