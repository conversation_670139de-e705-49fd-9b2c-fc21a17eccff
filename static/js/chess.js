/**
 * Chess board visualization and interaction
 */

class ChessBoard {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.selectedSquare = null;
        this.legalMoves = [];
        this.lastMove = null;
        this.isFlipped = false;
        this.pieceSymbols = {
            'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙',
            'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟'
        };
        this.onSquareClick = null;
        this.init();
    }

    init() {
        this.createBoard();
    }

    createBoard() {
        this.container.innerHTML = '';
        
        for (let rank = 8; rank >= 1; rank--) {
            for (let file = 1; file <= 8; file++) {
                const square = document.createElement('div');
                const fileChar = String.fromCharCode(96 + file); // a-h
                const squareName = fileChar + rank;
                
                square.className = 'square';
                square.dataset.square = squareName;
                
                // Determine square color
                const isLight = (file + rank) % 2 === 0;
                square.classList.add(isLight ? 'light' : 'dark');
                
                // Add click handler
                square.addEventListener('click', (e) => {
                    if (this.onSquareClick) {
                        this.onSquareClick(squareName, e);
                    }
                });
                
                this.container.appendChild(square);
            }
        }
    }

    setPosition(fen) {
        // Clear all pieces
        const squares = this.container.querySelectorAll('.square');
        squares.forEach(square => {
            square.textContent = '';
        });

        // Parse FEN and place pieces
        const fenParts = fen.split(' ');
        const position = fenParts[0];
        const ranks = position.split('/');
        
        for (let rankIndex = 0; rankIndex < 8; rankIndex++) {
            const rank = ranks[rankIndex];
            let fileIndex = 0;
            
            for (let char of rank) {
                if (isNaN(char)) {
                    // It's a piece
                    const file = String.fromCharCode(97 + fileIndex); // a-h
                    const rankNum = 8 - rankIndex;
                    const squareName = file + rankNum;
                    const square = this.getSquare(squareName);
                    
                    if (square && this.pieceSymbols[char]) {
                        square.textContent = this.pieceSymbols[char];
                    }
                    fileIndex++;
                } else {
                    // It's a number (empty squares)
                    fileIndex += parseInt(char);
                }
            }
        }
    }

    getSquare(squareName) {
        return this.container.querySelector(`[data-square="${squareName}"]`);
    }

    clearHighlights() {
        const squares = this.container.querySelectorAll('.square');
        squares.forEach(square => {
            square.classList.remove('selected', 'legal-move', 'last-move');
        });
    }

    highlightSquare(squareName, className = 'selected') {
        const square = this.getSquare(squareName);
        if (square) {
            square.classList.add(className);
        }
    }

    highlightLegalMoves(moves) {
        this.clearHighlights();
        moves.forEach(move => {
            // Extract destination square from UCI notation
            const toSquare = move.slice(2, 4);
            this.highlightSquare(toSquare, 'legal-move');
        });
    }

    highlightLastMove(move) {
        if (move) {
            const fromSquare = move.slice(0, 2);
            const toSquare = move.slice(2, 4);
            this.highlightSquare(fromSquare, 'last-move');
            this.highlightSquare(toSquare, 'last-move');
        }
    }

    selectSquare(squareName) {
        this.clearHighlights();
        this.selectedSquare = squareName;
        this.highlightSquare(squareName, 'selected');
    }

    clearSelection() {
        this.selectedSquare = null;
        this.clearHighlights();
    }

    flip() {
        this.isFlipped = !this.isFlipped;
        this.container.style.transform = this.isFlipped ? 'rotate(180deg)' : 'none';
        
        // Flip individual squares back so pieces appear correctly
        const squares = this.container.querySelectorAll('.square');
        squares.forEach(square => {
            square.style.transform = this.isFlipped ? 'rotate(180deg)' : 'none';
        });
    }

    setFlipped(flipped) {
        if (this.isFlipped !== flipped) {
            this.flip();
        }
    }

    isSquareOccupied(squareName) {
        const square = this.getSquare(squareName);
        return square && square.textContent.trim() !== '';
    }

    getPieceAt(squareName) {
        const square = this.getSquare(squareName);
        return square ? square.textContent.trim() : null;
    }

    animateMove(fromSquare, toSquare, callback) {
        // Simple animation - in a real implementation, you might want smoother animations
        const from = this.getSquare(fromSquare);
        const to = this.getSquare(toSquare);
        
        if (from && to) {
            const piece = from.textContent;
            from.textContent = '';
            to.textContent = piece;
            
            // Highlight the move briefly
            this.highlightSquare(fromSquare, 'last-move');
            this.highlightSquare(toSquare, 'last-move');
            
            if (callback) {
                setTimeout(callback, 300);
            }
        }
    }
}
