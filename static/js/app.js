/**
 * Main application logic for Chess AI
 */

class ChessApp {
    constructor() {
        this.board = new ChessBoard('chess-board');
        this.gameState = null;
        this.sessionId = null;
        this.isWaitingForAI = false;
        this.gameDurationInterval = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadEngineInfo();
        this.startNewGame();
    }

    setupEventListeners() {
        // Board interaction
        this.board.onSquareClick = (square, event) => {
            this.handleSquareClick(square, event);
        };

        // Control buttons
        document.getElementById('new-game-btn').addEventListener('click', () => {
            this.showNewGameModal();
        });

        document.getElementById('switch-color-btn').addEventListener('click', () => {
            this.switchColors();
        });

        document.getElementById('get-suggestions-btn').addEventListener('click', () => {
            this.getSuggestions();
        });

        document.getElementById('reset-game-btn').addEventListener('click', () => {
            this.resetGame();
        });

        document.getElementById('make-move-btn').addEventListener('click', () => {
            this.makeManualMove();
        });

        // Move input
        document.getElementById('move-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.makeManualMove();
            }
        });

        // Modal handlers
        document.getElementById('confirm-new-game').addEventListener('click', () => {
            this.confirmNewGame();
        });

        document.getElementById('cancel-new-game').addEventListener('click', () => {
            this.hideNewGameModal();
        });

        // Suggestion clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.suggestion-item')) {
                const suggestionItem = e.target.closest('.suggestion-item');
                const move = suggestionItem.dataset.move;
                if (move) {
                    document.getElementById('move-input').value = move;
                }
            }
        });
    }

    async startNewGame(humanColor = 'white', difficulty = 'intermediate') {
        try {
            this.showLoading('Starting new game...');
            
            const response = await fetch('/api/new_game', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    human_color: humanColor,
                    difficulty: difficulty
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.sessionId = data.session_id;
                this.updateGameState(data.game_state);
                
                // If AI made the first move
                if (data.ai_move) {
                    this.handleAIMove(data.ai_move);
                }
                
                this.startGameDurationTimer();
            } else {
                this.showError('Failed to start new game: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async makeMove(move) {
        if (this.isWaitingForAI) {
            return;
        }

        try {
            this.showLoading('Making move...');
            
            const response = await fetch('/api/make_move', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ move: move })
            });

            const data = await response.json();
            
            if (data.success) {
                this.updateGameState(data.human_move.game_state);
                this.board.highlightLastMove(move);
                
                // Clear move input
                document.getElementById('move-input').value = '';
                
                // Handle AI response
                if (data.ai_move) {
                    this.handleAIMove(data.ai_move);
                }
            } else {
                this.showError('Invalid move: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    handleAIMove(aiMoveData) {
        if (aiMoveData.success) {
            const aiMove = aiMoveData.move.uci;
            this.board.highlightLastMove(aiMove);
            this.updateGameState(aiMoveData.game_state);
            
            // Show AI thinking time
            if (aiMoveData.think_time) {
                this.showMessage(`AI thought for ${aiMoveData.think_time.toFixed(2)}s`);
            }
        } else {
            this.showError('AI move failed: ' + aiMoveData.error);
        }
    }

    async getSuggestions() {
        try {
            this.showLoading('Getting suggestions...');
            
            const response = await fetch('/api/get_suggestions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ count: 3 })
            });

            const data = await response.json();
            
            if (data.success) {
                this.displaySuggestions(data.suggestions);
            } else {
                this.showError('Failed to get suggestions: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async switchColors() {
        try {
            this.showLoading('Switching colors...');
            
            const response = await fetch('/api/switch_color', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            
            if (data.success) {
                this.updateGameState(data.game_state);
                this.board.setFlipped(data.human_color === 'black');
                
                // If AI made a move after switching
                if (data.ai_move) {
                    this.handleAIMove(data.ai_move);
                }
            } else {
                this.showError('Failed to switch colors: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async resetGame() {
        try {
            this.showLoading('Resetting game...');
            
            const response = await fetch('/api/reset_game', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            
            if (data.success) {
                this.updateGameState(data.game_state);
                this.clearSuggestions();
                this.clearMoveHistory();
                
                // If AI made the first move after reset
                if (data.ai_move) {
                    this.handleAIMove(data.ai_move);
                }
                
                this.startGameDurationTimer();
            } else {
                this.showError('Failed to reset game: ' + data.error);
            }
        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    handleSquareClick(square, event) {
        if (this.isWaitingForAI || !this.gameState) {
            return;
        }

        // Check if it's human's turn
        if (this.gameState.current_player !== 'human') {
            this.showMessage("It's not your turn!");
            return;
        }

        if (this.board.selectedSquare) {
            // Try to make a move
            const move = this.board.selectedSquare + square;
            this.makeMove(move);
            this.board.clearSelection();
        } else {
            // Select the square if it has a piece
            if (this.board.isSquareOccupied(square)) {
                this.board.selectSquare(square);
                // Show legal moves for this piece
                this.showLegalMovesForSquare(square);
            }
        }
    }

    showLegalMovesForSquare(square) {
        if (this.gameState && this.gameState.legal_moves) {
            const legalMoves = this.gameState.legal_moves.filter(move => 
                move.startsWith(square)
            );
            this.board.highlightLegalMoves(legalMoves);
        }
    }

    makeManualMove() {
        const moveInput = document.getElementById('move-input');
        const move = moveInput.value.trim().toLowerCase();
        
        if (move) {
            this.makeMove(move);
        }
    }

    updateGameState(gameState) {
        this.gameState = gameState;
        
        // Update board position
        this.board.setPosition(gameState.fen);
        
        // Update UI elements
        this.updatePlayerInfo();
        this.updateTurnIndicator();
        this.updateGameInfo();
        this.updateMoveHistory();
        
        // Check for game over
        if (gameState.is_game_over) {
            this.handleGameOver();
        }
    }

    updatePlayerInfo() {
        document.getElementById('human-color').textContent = 
            this.gameState.human_color.charAt(0).toUpperCase() + this.gameState.human_color.slice(1);
        document.getElementById('ai-color').textContent = 
            this.gameState.ai_color.charAt(0).toUpperCase() + this.gameState.ai_color.slice(1);
    }

    updateTurnIndicator() {
        const turnElement = document.getElementById('current-turn');
        const currentPlayer = this.gameState.current_player;
        const currentColor = this.gameState.turn;
        
        if (this.gameState.is_game_over) {
            turnElement.textContent = 'Game Over';
        } else {
            turnElement.textContent = `${currentColor.charAt(0).toUpperCase() + currentColor.slice(1)} to move (${currentPlayer})`;
        }
    }

    updateGameInfo() {
        document.getElementById('move-count').textContent = this.gameState.move_count;
        
        if (this.gameState.game_duration) {
            const duration = Math.floor(this.gameState.game_duration);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            document.getElementById('game-duration').textContent = 
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    updateMoveHistory() {
        const historyElement = document.getElementById('move-history');
        
        if (this.gameState.move_count === 0) {
            historyElement.innerHTML = '<p class="no-moves">No moves yet</p>';
            return;
        }

        // This would need to be implemented based on the actual move history structure
        // For now, show a simple placeholder
        historyElement.innerHTML = `<p>Moves: ${this.gameState.move_count}</p>`;
    }

    displaySuggestions(suggestions) {
        const suggestionsElement = document.getElementById('suggestions-list');
        
        if (suggestions.length === 0) {
            suggestionsElement.innerHTML = '<p class="no-suggestions">No suggestions available</p>';
            return;
        }

        const suggestionsHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" data-move="${suggestion.move}">
                <div class="suggestion-move">${suggestion.san} (${suggestion.move})</div>
                <div class="suggestion-eval">Rank ${suggestion.rank}: ${suggestion.evaluation}</div>
            </div>
        `).join('');

        suggestionsElement.innerHTML = suggestionsHTML;
    }

    clearSuggestions() {
        document.getElementById('suggestions-list').innerHTML = 
            '<p class="no-suggestions">Click "Get Suggestions" to see AI recommendations</p>';
    }

    clearMoveHistory() {
        document.getElementById('move-history').innerHTML = 
            '<p class="no-moves">No moves yet</p>';
    }

    handleGameOver() {
        const resultElement = document.getElementById('game-result');
        resultElement.textContent = this.gameState.result_description || 'Game Over';
        resultElement.style.display = 'block';
        
        // Add appropriate CSS class based on result
        if (this.gameState.result_description) {
            if (this.gameState.result_description.includes('wins')) {
                resultElement.className = 'game-result win';
            } else if (this.gameState.result_description.includes('draw') || 
                       this.gameState.result_description.includes('Stalemate')) {
                resultElement.className = 'game-result draw';
            } else {
                resultElement.className = 'game-result lose';
            }
        }
        
        this.stopGameDurationTimer();
    }

    showNewGameModal() {
        document.getElementById('new-game-modal').style.display = 'flex';
    }

    hideNewGameModal() {
        document.getElementById('new-game-modal').style.display = 'none';
    }

    confirmNewGame() {
        const selectedColor = document.querySelector('input[name="color"]:checked').value;
        const selectedDifficulty = document.getElementById('modal-difficulty').value;
        
        this.hideNewGameModal();
        this.startNewGame(selectedColor, selectedDifficulty);
    }

    showLoading(message = 'Loading...') {
        this.isWaitingForAI = true;
        document.getElementById('loading-text').textContent = message;
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        this.isWaitingForAI = false;
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showError(message) {
        alert('Error: ' + message);
    }

    showMessage(message) {
        // Simple message display - could be improved with a toast system
        console.log('Message:', message);
    }

    async loadEngineInfo() {
        try {
            const response = await fetch('/api/engine_info');
            const data = await response.json();
            
            if (data.success) {
                const engineStatus = data.engine_info.is_ready ? 'Ready' : 'Not Ready';
                document.getElementById('engine-status').textContent = engineStatus;
            }
        } catch (error) {
            document.getElementById('engine-status').textContent = 'Error';
        }
    }

    startGameDurationTimer() {
        this.stopGameDurationTimer();
        this.gameDurationInterval = setInterval(() => {
            if (this.gameState && !this.gameState.is_game_over) {
                this.updateGameInfo();
            }
        }, 1000);
    }

    stopGameDurationTimer() {
        if (this.gameDurationInterval) {
            clearInterval(this.gameDurationInterval);
            this.gameDurationInterval = null;
        }
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chessApp = new ChessApp();
});
