"""Stockfish AI integration for chess engine."""

import chess
import chess.engine
from stockfish import Stockfish
from typing import List, Dict, Any, Optional, Tuple
import time
import logging
from config import Config

logger = logging.getLogger(__name__)


class StockfishAI:
    """Stockfish AI engine wrapper with advanced features."""
    
    def __init__(self, stockfish_path: str = None, difficulty: str = 'intermediate'):
        """Initialize Stockfish AI.
        
        Args:
            stockfish_path: Path to Stockfish executable
            difficulty: Difficulty level (beginner, intermediate, advanced, expert, master, grandmaster)
        """
        self.stockfish_path = stockfish_path or Config.STOCKFISH_PATH
        self.difficulty = difficulty
        self.think_time = Config.AI_THINK_TIME
        self.suggestions_count = Config.AI_SUGGESTIONS_COUNT
        
        # Initialize Stockfish
        try:
            self.stockfish = Stockfish(
                path=self.stockfish_path,
                depth=Config.AI_DEPTH,
                parameters={
                    "Threads": 4,
                    "Hash": 256,
                    "UCI_Elo": Config.DIFFICULTY_LEVELS.get(difficulty, 1200),
                    "UCI_LimitStrength": True
                }
            )
            logger.info(f"Stockfish initialized with difficulty: {difficulty}")
        except Exception as e:
            logger.error(f"Failed to initialize Stockfish: {e}")
            raise
    
    def set_difficulty(self, difficulty: str) -> bool:
        """Set AI difficulty level.
        
        Args:
            difficulty: New difficulty level
            
        Returns:
            True if successful, False otherwise
        """
        if difficulty not in Config.DIFFICULTY_LEVELS:
            logger.warning(f"Invalid difficulty level: {difficulty}")
            return False
        
        try:
            elo_rating = Config.DIFFICULTY_LEVELS[difficulty]
            self.stockfish.set_elo_rating(elo_rating)
            self.difficulty = difficulty
            logger.info(f"Difficulty set to {difficulty} (Elo: {elo_rating})")
            return True
        except Exception as e:
            logger.error(f"Failed to set difficulty: {e}")
            return False
    
    def get_best_move(self, board: chess.Board, time_limit: float = None) -> Optional[chess.Move]:
        """Get the best move from Stockfish.
        
        Args:
            board: Current chess board position
            time_limit: Time limit in seconds (uses default if None)
            
        Returns:
            Best move or None if no move found
        """
        if time_limit is None:
            time_limit = self.think_time
        
        try:
            # Set the position
            self.stockfish.set_fen_position(board.fen())
            
            # Get the best move with time limit
            start_time = time.time()
            best_move_uci = self.stockfish.get_best_move_time(int(time_limit * 1000))  # Convert to milliseconds
            elapsed_time = time.time() - start_time
            
            if best_move_uci:
                move = chess.Move.from_uci(best_move_uci)
                logger.info(f"Best move found: {move.uci()} (took {elapsed_time:.2f}s)")
                return move
            else:
                logger.warning("No best move found")
                return None
                
        except Exception as e:
            logger.error(f"Error getting best move: {e}")
            return None
    
    def get_move_suggestions(self, board: chess.Board, count: int = None) -> List[Dict[str, Any]]:
        """Get multiple move suggestions with evaluations.
        
        Args:
            board: Current chess board position
            count: Number of suggestions to return (uses default if None)
            
        Returns:
            List of move suggestions with evaluations
        """
        if count is None:
            count = self.suggestions_count
        
        suggestions = []
        
        try:
            # Set the position
            self.stockfish.set_fen_position(board.fen())
            
            # Get top moves
            top_moves = self.stockfish.get_top_moves(count)
            
            for i, move_info in enumerate(top_moves):
                move_uci = move_info.get('Move')
                centipawn = move_info.get('Centipawn')
                mate = move_info.get('Mate')
                
                if move_uci:
                    try:
                        move = chess.Move.from_uci(move_uci)
                        
                        # Create a copy of the board to make the move
                        temp_board = board.copy()
                        temp_board.push(move)
                        
                        suggestion = {
                            'move': move.uci(),
                            'san': board.san(move),
                            'rank': i + 1,
                            'evaluation': self._format_evaluation(centipawn, mate),
                            'centipawn': centipawn,
                            'mate': mate
                        }
                        
                        suggestions.append(suggestion)
                        
                    except Exception as e:
                        logger.warning(f"Error processing move {move_uci}: {e}")
                        continue
            
            logger.info(f"Generated {len(suggestions)} move suggestions")
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting move suggestions: {e}")
            return []
    
    def evaluate_position(self, board: chess.Board) -> Dict[str, Any]:
        """Evaluate the current position.
        
        Args:
            board: Current chess board position
            
        Returns:
            Position evaluation information
        """
        try:
            self.stockfish.set_fen_position(board.fen())
            evaluation = self.stockfish.get_evaluation()
            
            return {
                'type': evaluation.get('type'),
                'value': evaluation.get('value'),
                'formatted': self._format_evaluation(
                    evaluation.get('value') if evaluation.get('type') == 'cp' else None,
                    evaluation.get('value') if evaluation.get('type') == 'mate' else None
                )
            }
            
        except Exception as e:
            logger.error(f"Error evaluating position: {e}")
            return {'type': 'unknown', 'value': 0, 'formatted': 'Unknown'}
    
    def _format_evaluation(self, centipawn: Optional[int], mate: Optional[int]) -> str:
        """Format evaluation for display.
        
        Args:
            centipawn: Centipawn evaluation
            mate: Mate in X moves
            
        Returns:
            Formatted evaluation string
        """
        if mate is not None:
            if mate > 0:
                return f"Mate in {mate}"
            elif mate < 0:
                return f"Mate in {abs(mate)} (opponent)"
            else:
                return "Mate"
        elif centipawn is not None:
            # Convert centipawns to pawns
            pawns = centipawn / 100.0
            if pawns > 0:
                return f"+{pawns:.2f}"
            else:
                return f"{pawns:.2f}"
        else:
            return "0.00"
    
    def is_engine_ready(self) -> bool:
        """Check if the Stockfish engine is ready.
        
        Returns:
            True if engine is ready, False otherwise
        """
        try:
            return self.stockfish.is_fen_valid("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
        except:
            return False
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about the Stockfish engine.
        
        Returns:
            Engine information
        """
        return {
            'path': self.stockfish_path,
            'difficulty': self.difficulty,
            'elo_rating': Config.DIFFICULTY_LEVELS.get(self.difficulty, 1200),
            'think_time': self.think_time,
            'suggestions_count': self.suggestions_count,
            'is_ready': self.is_engine_ready()
        }
