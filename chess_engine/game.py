"""Chess game state management."""

import chess
import chess.pgn
from typing import List, Dict, Any, Optional, Tuple
import time
import logging
from datetime import datetime

from .stockfish_ai import StockfishAI
from .utils import move_to_dict, board_to_dict, get_game_result_description

logger = logging.getLogger(__name__)


class ChessGame:
    """Chess game state manager with AI integration."""
    
    def __init__(self, human_color: str = 'white', difficulty: str = 'intermediate'):
        """Initialize a new chess game.
        
        Args:
            human_color: Color for human player ('white' or 'black')
            difficulty: AI difficulty level
        """
        self.board = chess.Board()
        self.human_color = human_color.lower()
        self.ai_color = 'black' if self.human_color == 'white' else 'white'
        self.move_history = []
        self.game_start_time = time.time()
        self.last_move_time = None
        
        # Initialize AI
        try:
            self.ai = StockfishAI(difficulty=difficulty)
            logger.info(f"Chess game initialized: Human={human_color}, AI={self.ai_color}, Difficulty={difficulty}")
        except Exception as e:
            logger.error(f"Failed to initialize AI: {e}")
            raise
        
        # Game metadata
        self.metadata = {
            'start_time': datetime.now().isoformat(),
            'human_color': self.human_color,
            'ai_color': self.ai_color,
            'difficulty': difficulty,
            'result': None,
            'total_moves': 0
        }
    
    def make_human_move(self, move_str: str) -> Dict[str, Any]:
        """Make a move for the human player.
        
        Args:
            move_str: Move in UCI notation (e.g., 'e2e4')
            
        Returns:
            Result of the move attempt
        """
        try:
            # Validate it's human's turn
            current_turn = 'white' if self.board.turn else 'black'
            if current_turn != self.human_color:
                return {
                    'success': False,
                    'error': f"It's not your turn. Current turn: {current_turn}",
                    'game_state': self.get_game_state()
                }
            
            # Parse and validate move
            try:
                move = chess.Move.from_uci(move_str)
            except ValueError:
                return {
                    'success': False,
                    'error': f"Invalid move format: {move_str}",
                    'game_state': self.get_game_state()
                }
            
            # Check if move is legal
            if move not in self.board.legal_moves:
                return {
                    'success': False,
                    'error': f"Illegal move: {move_str}",
                    'game_state': self.get_game_state()
                }
            
            # Make the move
            self.board.push(move)
            self.last_move_time = time.time()
            
            # Record move in history
            move_record = {
                'move': move_to_dict(move, self.board),
                'player': 'human',
                'color': self.human_color,
                'timestamp': self.last_move_time,
                'fen_after': self.board.fen()
            }
            self.move_history.append(move_record)
            self.metadata['total_moves'] += 1
            
            logger.info(f"Human move made: {move.uci()}")
            
            # Check if game is over
            game_state = self.get_game_state()
            if game_state['is_game_over']:
                self.metadata['result'] = game_state['result_description']
                logger.info(f"Game over: {self.metadata['result']}")
            
            return {
                'success': True,
                'move': move_to_dict(move, self.board),
                'game_state': game_state
            }
            
        except Exception as e:
            logger.error(f"Error making human move: {e}")
            return {
                'success': False,
                'error': f"Internal error: {str(e)}",
                'game_state': self.get_game_state()
            }
    
    def make_ai_move(self, time_limit: float = None) -> Dict[str, Any]:
        """Make a move for the AI player.
        
        Args:
            time_limit: Time limit for AI thinking (uses default if None)
            
        Returns:
            Result of the AI move
        """
        try:
            # Validate it's AI's turn
            current_turn = 'white' if self.board.turn else 'black'
            if current_turn != self.ai_color:
                return {
                    'success': False,
                    'error': f"It's not AI's turn. Current turn: {current_turn}",
                    'game_state': self.get_game_state()
                }
            
            # Check if game is already over
            if self.board.is_game_over():
                return {
                    'success': False,
                    'error': "Game is already over",
                    'game_state': self.get_game_state()
                }
            
            # Get AI move
            start_time = time.time()
            ai_move = self.ai.get_best_move(self.board, time_limit)
            think_time = time.time() - start_time
            
            if ai_move is None:
                return {
                    'success': False,
                    'error': "AI could not find a move",
                    'game_state': self.get_game_state()
                }
            
            # Make the move
            self.board.push(ai_move)
            self.last_move_time = time.time()
            
            # Record move in history
            move_record = {
                'move': move_to_dict(ai_move, self.board),
                'player': 'ai',
                'color': self.ai_color,
                'timestamp': self.last_move_time,
                'think_time': think_time,
                'fen_after': self.board.fen()
            }
            self.move_history.append(move_record)
            self.metadata['total_moves'] += 1
            
            logger.info(f"AI move made: {ai_move.uci()} (think time: {think_time:.2f}s)")
            
            # Check if game is over
            game_state = self.get_game_state()
            if game_state['is_game_over']:
                self.metadata['result'] = game_state['result_description']
                logger.info(f"Game over: {self.metadata['result']}")
            
            return {
                'success': True,
                'move': move_to_dict(ai_move, self.board),
                'think_time': think_time,
                'game_state': game_state
            }
            
        except Exception as e:
            logger.error(f"Error making AI move: {e}")
            return {
                'success': False,
                'error': f"AI error: {str(e)}",
                'game_state': self.get_game_state()
            }
    
    def get_move_suggestions(self, count: int = 3) -> List[Dict[str, Any]]:
        """Get move suggestions for the current position.
        
        Args:
            count: Number of suggestions to return
            
        Returns:
            List of move suggestions
        """
        try:
            return self.ai.get_move_suggestions(self.board, count)
        except Exception as e:
            logger.error(f"Error getting move suggestions: {e}")
            return []
    
    def switch_human_color(self) -> Dict[str, Any]:
        """Switch the human player's color.
        
        Returns:
            Result of the color switch
        """
        try:
            # Can only switch at the beginning of the game
            if len(self.move_history) > 0:
                return {
                    'success': False,
                    'error': "Cannot switch colors after moves have been made"
                }
            
            # Switch colors
            self.human_color = 'black' if self.human_color == 'white' else 'white'
            self.ai_color = 'white' if self.human_color == 'black' else 'black'
            
            # Update metadata
            self.metadata['human_color'] = self.human_color
            self.metadata['ai_color'] = self.ai_color
            
            logger.info(f"Colors switched: Human={self.human_color}, AI={self.ai_color}")
            
            return {
                'success': True,
                'human_color': self.human_color,
                'ai_color': self.ai_color,
                'game_state': self.get_game_state()
            }
            
        except Exception as e:
            logger.error(f"Error switching colors: {e}")
            return {
                'success': False,
                'error': f"Error switching colors: {str(e)}"
            }
    
    def get_game_state(self) -> Dict[str, Any]:
        """Get the current game state.
        
        Returns:
            Complete game state information
        """
        board_state = board_to_dict(self.board)
        
        return {
            **board_state,
            'human_color': self.human_color,
            'ai_color': self.ai_color,
            'current_player': 'human' if board_state['turn'] == self.human_color else 'ai',
            'move_count': len(self.move_history),
            'last_move': self.move_history[-1] if self.move_history else None,
            'result_description': get_game_result_description(self.board),
            'game_duration': time.time() - self.game_start_time,
            'metadata': self.metadata
        }
    
    def reset_game(self, human_color: str = None, difficulty: str = None) -> Dict[str, Any]:
        """Reset the game to initial state.
        
        Args:
            human_color: New human color (keeps current if None)
            difficulty: New AI difficulty (keeps current if None)
            
        Returns:
            New game state
        """
        try:
            # Reset board
            self.board = chess.Board()
            self.move_history = []
            self.game_start_time = time.time()
            self.last_move_time = None
            
            # Update colors if specified
            if human_color:
                self.human_color = human_color.lower()
                self.ai_color = 'black' if self.human_color == 'white' else 'white'
            
            # Update AI difficulty if specified
            if difficulty:
                self.ai.set_difficulty(difficulty)
            
            # Reset metadata
            self.metadata = {
                'start_time': datetime.now().isoformat(),
                'human_color': self.human_color,
                'ai_color': self.ai_color,
                'difficulty': difficulty or self.ai.difficulty,
                'result': None,
                'total_moves': 0
            }
            
            logger.info("Game reset successfully")
            
            return {
                'success': True,
                'game_state': self.get_game_state()
            }
            
        except Exception as e:
            logger.error(f"Error resetting game: {e}")
            return {
                'success': False,
                'error': f"Error resetting game: {str(e)}"
            }
    
    def get_pgn(self) -> str:
        """Get the game in PGN format.
        
        Returns:
            PGN string representation of the game
        """
        try:
            game = chess.pgn.Game()
            
            # Set headers
            game.headers["Event"] = "Chess AI Game"
            game.headers["Date"] = datetime.now().strftime("%Y.%m.%d")
            game.headers["White"] = "Human" if self.human_color == 'white' else "AI"
            game.headers["Black"] = "AI" if self.human_color == 'white' else "Human"
            game.headers["Result"] = self.metadata.get('result', '*')
            
            # Add moves
            node = game
            temp_board = chess.Board()
            
            for move_record in self.move_history:
                move_uci = move_record['move']['uci']
                move = chess.Move.from_uci(move_uci)
                node = node.add_variation(move)
                temp_board.push(move)
            
            return str(game)
            
        except Exception as e:
            logger.error(f"Error generating PGN: {e}")
            return ""
