"""Utility functions for the chess engine."""

import chess
from typing import List, Dict, Any, Optional


def move_to_dict(move: chess.Move, board: chess.Board) -> Dict[str, Any]:
    """Convert a chess move to a dictionary representation."""
    # Create a copy of the board to avoid modifying the original
    temp_board = board.copy()

    # Check if the move is legal before getting SAN notation
    if move in temp_board.legal_moves:
        san = temp_board.san(move)
    else:
        # If move is not legal, just use UCI notation
        san = move.uci()

    return {
        'from': chess.square_name(move.from_square),
        'to': chess.square_name(move.to_square),
        'promotion': move.promotion,
        'uci': move.uci(),
        'san': san
    }


def board_to_dict(board: chess.Board) -> Dict[str, Any]:
    """Convert a chess board to a dictionary representation."""
    return {
        'fen': board.fen(),
        'turn': 'white' if board.turn else 'black',
        'castling_rights': {
            'white_kingside': board.has_kingside_castling_rights(chess.WHITE),
            'white_queenside': board.has_queenside_castling_rights(chess.WHITE),
            'black_kingside': board.has_kingside_castling_rights(chess.BLACK),
            'black_queenside': board.has_queenside_castling_rights(chess.BLACK),
        },
        'en_passant': chess.square_name(board.ep_square) if board.ep_square else None,
        'halfmove_clock': board.halfmove_clock,
        'fullmove_number': board.fullmove_number,
        'is_check': board.is_check(),
        'is_checkmate': board.is_checkmate(),
        'is_stalemate': board.is_stalemate(),
        'is_insufficient_material': board.is_insufficient_material(),
        'is_game_over': board.is_game_over(),
        'legal_moves': [move.uci() for move in board.legal_moves]
    }


def get_piece_positions(board: chess.Board) -> Dict[str, List[str]]:
    """Get positions of all pieces on the board."""
    pieces = {}
    
    for square in chess.SQUARES:
        piece = board.piece_at(square)
        if piece:
            piece_symbol = piece.symbol()
            square_name = chess.square_name(square)
            
            if piece_symbol not in pieces:
                pieces[piece_symbol] = []
            pieces[piece_symbol].append(square_name)
    
    return pieces


def validate_move_input(move_str: str) -> bool:
    """Validate if a move string is in correct UCI format."""
    if not isinstance(move_str, str):
        return False
    
    # Basic UCI format validation (e.g., "e2e4", "e7e8q")
    if len(move_str) < 4 or len(move_str) > 5:
        return False
    
    # Check if squares are valid
    try:
        from_square = move_str[:2]
        to_square = move_str[2:4]
        
        # Validate square names
        chess.parse_square(from_square)
        chess.parse_square(to_square)
        
        # If there's a 5th character, it should be a promotion piece
        if len(move_str) == 5:
            promotion = move_str[4].lower()
            if promotion not in ['q', 'r', 'b', 'n']:
                return False
        
        return True
    except:
        return False


def format_time(seconds: float) -> str:
    """Format time in seconds to a human-readable string."""
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    else:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.1f}s"


def get_game_result_description(board: chess.Board) -> Optional[str]:
    """Get a human-readable description of the game result."""
    if not board.is_game_over():
        return None
    
    if board.is_checkmate():
        winner = "Black" if board.turn else "White"
        return f"Checkmate! {winner} wins."
    elif board.is_stalemate():
        return "Stalemate! The game is a draw."
    elif board.is_insufficient_material():
        return "Draw due to insufficient material."
    elif board.is_seventyfive_moves():
        return "Draw due to 75-move rule."
    elif board.is_fivefold_repetition():
        return "Draw due to fivefold repetition."
    else:
        return "Game over."
