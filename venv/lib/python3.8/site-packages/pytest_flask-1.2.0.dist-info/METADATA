Metadata-Version: 2.1
Name: pytest-flask
Version: 1.2.0
Summary: A set of py.test fixtures to test Flask applications.
Home-page: https://github.com/pytest-dev/pytest-flask
Author: <PERSON><PERSON>
Author-email: vital.kud<PERSON><EMAIL>
License: MIT
Project-URL: Source, https://github.com/pytest-dev/pytest-flask
Project-URL: Issue tracker, https://github.com/pytest-dev/pytest-flask/issues
Keywords: pytest,flask,testing
Platform: UNKNOWN
Classifier: Framework :: Pytest
Classifier: Environment :: Plugins
Classifier: Programming Language :: Python
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development :: Testing
Classifier: Development Status :: 5 - Production/Stable
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.5
Requires-Dist: pytest (>=5.2)
Requires-Dist: Flask
Requires-Dist: Werkzeug (>=0.7)
Provides-Extra: docs
Requires-Dist: Sphinx ; extra == 'docs'
Requires-Dist: sphinx-rtd-theme ; extra == 'docs'
Provides-Extra: tests

pytest-flask
============

.. image:: https://img.shields.io/pypi/v/pytest-flask.svg
    :target: https://pypi.python.org/pypi/pytest-flask
    :alt: PyPi version

.. image:: https://img.shields.io/conda/vn/conda-forge/pytest-flask.svg
    :target: https://anaconda.org/conda-forge/pytest-flask
    :alt: conda-forge version

.. image:: https://github.com/pytest-dev/pytest-flask/workflows/build/badge.svg
    :target: https://github.com/pytest-dev/pytest-flask/actions
    :alt: CI status

.. image:: https://img.shields.io/pypi/pyversions/pytest-flask.svg
    :target: https://pypi.org/project/pytest-flask
    :alt: PyPi downloads

.. image:: https://readthedocs.org/projects/pytest-flask/badge/?version=latest
   :target: https://pytest-flask.readthedocs.org/en/latest/
   :alt: Documentation status

.. image:: https://img.shields.io/maintenance/yes/2020?color=blue
    :target: https://github.com/pytest-dev/pytest-flask
    :alt: Maintenance

.. image:: https://img.shields.io/github/last-commit/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/commits/master
    :alt: GitHub last commit

.. image:: https://img.shields.io/github/issues-pr-closed-raw/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/pulls?q=is%3Apr+is%3Aclosed
    :alt: GitHub closed pull requests

.. image:: https://img.shields.io/github/issues-closed/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask/issues?q=is%3Aissue+is%3Aclosed
    :alt: GitHub closed issues

.. image:: https://img.shields.io/pypi/dm/pytest-flask?color=blue
    :target: https://pypi.org/project/pytest-flask/
    :alt: PyPI - Downloads

.. image:: https://img.shields.io/github/languages/code-size/pytest-dev/pytest-flask?color=blue
    :target: https://github.com/pytest-dev/pytest-flask
    :alt: Code size

.. image:: https://img.shields.io/badge/license-MIT-blue.svg?color=blue
   :target: https://github.com/pytest-dev/pytest-flask/blob/master/LICENSE
   :alt: License

.. image:: https://img.shields.io/github/issues-raw/pytest-dev/pytest-flask.svg?color=blue
   :target: https://github.com/pytest-dev/pytest-flask/issues
   :alt: Issues

.. image:: https://img.shields.io/badge/code%20style-black-000000.svg
   :target: https://github.com/ambv/black
   :alt: style

An extension of `pytest`_ test runner which
provides a set of useful tools to simplify testing and development
of the Flask extensions and applications.

To view a more detailed list of extension features and examples go to
the `PyPI`_ overview page or
`package documentation`_.

How to start?
-------------

Considering the minimal flask `application factory`_ bellow in ``myapp.py`` as an example:

.. code-block:: python

   from flask import Flask

   def create_app(config_filename):
      # create a minimal app
      app = Flask(__name__)
      app.config.from_pyfile(config_filename)

      # simple hello world view
      @app.route('/hello')
      def hello():
         return 'Hello, World!'

      return app

You first need to define your application fixture in ``conftest.py``:

.. code-block:: python

    from myapp import create_app

    @pytest.fixture
    def app():
        app = create_app()
        return app

Finally, install the extension with dependencies and run your test suite::

    $ pip install pytest-flask
    $ pytest

Contributing
------------

Don’t hesitate to create a `GitHub issue`_ for any bug or
suggestion. For more information check our contribution `guidelines`_.

.. _pytest: https://docs.pytest.org/en/stable/
.. _PyPI: https://pypi.python.org/pypi/pytest-flask
.. _Github issue: https://github.com/vitalk/pytest-flask/issues
.. _package documentation: http://pytest-flask.readthedocs.org/en/latest/
.. _guidelines: https://github.com/pytest-dev/pytest-flask/blob/master/CONTRIBUTING.rst
.. _application factory: https://flask.palletsprojects.com/en/1.1.x/patterns/appfactories/

The MIT License (MIT)

Copyright © 2014–2016 Vital Kudzelka and contributors.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the “Software”), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


