"""Main Flask application for Chess AI."""

from flask import <PERSON>lask, render_template, request, jsonify, session
from flask_cors import CORS
import logging
import os
from datetime import datetime

from chess_engine import <PERSON><PERSON>ame
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Global game storage (in production, use a proper database)
games = {}


def get_or_create_game(session_id: str) -> ChessGame:
    """Get existing game or create a new one for the session."""
    if session_id not in games:
        games[session_id] = ChessGame()
        logger.info(f"Created new game for session: {session_id}")
    return games[session_id]


@app.route('/')
def index():
    """Main game interface."""
    return render_template('index.html')


@app.route('/api/new_game', methods=['POST'])
def new_game():
    """Start a new chess game."""
    try:
        data = request.get_json() or {}
        session_id = session.get('session_id', os.urandom(16).hex())
        session['session_id'] = session_id
        
        human_color = data.get('human_color', 'white')
        difficulty = data.get('difficulty', Config.DEFAULT_DIFFICULTY)
        
        # Create new game
        game = ChessGame(human_color=human_color, difficulty=difficulty)
        games[session_id] = game
        
        logger.info(f"New game started: session={session_id}, human_color={human_color}, difficulty={difficulty}")
        
        # If AI plays white, make the first move
        response_data = {
            'success': True,
            'session_id': session_id,
            'game_state': game.get_game_state()
        }
        
        if game.ai_color == 'white':
            ai_result = game.make_ai_move()
            response_data['ai_move'] = ai_result
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error creating new game: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/make_move', methods=['POST'])
def make_move():
    """Make a move in the current game."""
    try:
        data = request.get_json()
        if not data or 'move' not in data:
            return jsonify({
                'success': False,
                'error': 'Move is required'
            }), 400
        
        session_id = session.get('session_id')
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        move_str = data['move']
        
        # Make human move
        human_result = game.make_human_move(move_str)
        
        if not human_result['success']:
            return jsonify(human_result), 400
        
        response_data = {
            'success': True,
            'human_move': human_result,
            'ai_move': None
        }
        
        # If game is not over and it's AI's turn, make AI move
        if not human_result['game_state']['is_game_over']:
            ai_result = game.make_ai_move()
            response_data['ai_move'] = ai_result
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error making move: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/game_state', methods=['GET'])
def get_game_state():
    """Get the current game state."""
    try:
        session_id = session.get('session_id')
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        
        return jsonify({
            'success': True,
            'game_state': game.get_game_state()
        })
        
    except Exception as e:
        logger.error(f"Error getting game state: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/get_suggestions', methods=['POST'])
def get_suggestions():
    """Get move suggestions for the current position."""
    try:
        data = request.get_json() or {}
        count = data.get('count', Config.AI_SUGGESTIONS_COUNT)
        
        session_id = session.get('session_id')
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        suggestions = game.get_move_suggestions(count)
        
        return jsonify({
            'success': True,
            'suggestions': suggestions
        })
        
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/switch_color', methods=['POST'])
def switch_color():
    """Switch the human player's color."""
    try:
        session_id = session.get('session_id')
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        result = game.switch_human_color()
        
        # If AI now plays white, make the first move
        if result['success'] and game.ai_color == 'white':
            ai_result = game.make_ai_move()
            result['ai_move'] = ai_result
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error switching color: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/reset_game', methods=['POST'])
def reset_game():
    """Reset the current game."""
    try:
        data = request.get_json() or {}
        session_id = session.get('session_id')
        
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        human_color = data.get('human_color')
        difficulty = data.get('difficulty')
        
        result = game.reset_game(human_color, difficulty)
        
        # If AI plays white after reset, make the first move
        if result['success'] and game.ai_color == 'white':
            ai_result = game.make_ai_move()
            result['ai_move'] = ai_result
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error resetting game: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/get_pgn', methods=['GET'])
def get_pgn():
    """Get the current game in PGN format."""
    try:
        session_id = session.get('session_id')
        if not session_id or session_id not in games:
            return jsonify({
                'success': False,
                'error': 'No active game found'
            }), 404
        
        game = games[session_id]
        pgn = game.get_pgn()
        
        return jsonify({
            'success': True,
            'pgn': pgn
        })
        
    except Exception as e:
        logger.error(f"Error getting PGN: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/engine_info', methods=['GET'])
def get_engine_info():
    """Get information about the AI engine."""
    try:
        session_id = session.get('session_id')
        if session_id and session_id in games:
            game = games[session_id]
            engine_info = game.ai.get_engine_info()
        else:
            # Return default engine info
            from chess_engine import StockfishAI
            temp_ai = StockfishAI()
            engine_info = temp_ai.get_engine_info()
        
        return jsonify({
            'success': True,
            'engine_info': engine_info,
            'difficulty_levels': Config.DIFFICULTY_LEVELS
        })
        
    except Exception as e:
        logger.error(f"Error getting engine info: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500


if __name__ == '__main__':
    logger.info("Starting Chess AI application...")
    logger.info(f"Stockfish path: {Config.STOCKFISH_PATH}")
    logger.info(f"Default difficulty: {Config.DEFAULT_DIFFICULTY}")
    
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=Config.DEBUG
    )
