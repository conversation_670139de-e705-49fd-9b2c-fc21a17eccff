# Chess AI Web Application

A sophisticated web-based chess application where humans can play against the powerful Stockfish AI engine with advanced features and configurable difficulty levels.

## 🎯 Features

### Core Gameplay
- **Play against Stockfish AI**: Utilize the world's strongest chess engine
- **Flexible Color Selection**: Switch between playing as White or Black
- **Multiple Difficulty Levels**: From Beginner (800 Elo) to Grandmaster (3000 Elo)
- **Time-Controlled AI**: Configurable thinking time (default 10 seconds)
- **Move Validation**: Complete legal move checking and game state detection

### AI Assistance
- **Move Suggestions**: Get 3 AI-recommended moves with evaluations
- **Position Analysis**: Real-time position evaluation
- **Multiple Suggestion Ranks**: See the best moves ranked by strength

### Game Management
- **Complete Game State Tracking**: Checkmate, stalemate, draw detection
- **Move History**: Full game record with PGN export
- **Game Reset/New Game**: Start fresh anytime
- **Responsive Web Interface**: Works on desktop and mobile

### Technical Features
- **Real-time Updates**: Instant board updates and game state
- **Interactive Chess Board**: Click-to-move interface
- **Manual Move Input**: UCI notation support (e.g., e2e4)
- **Loading Indicators**: Visual feedback during AI thinking

## 🔧 Requirements

- **Python 3.8+**
- **Stockfish Chess Engine**
- **Modern Web Browser** (Chrome, Firefox, Safari, Edge)

## 🚀 Quick Start

### Automated Setup (Recommended)

1. **Clone the repository:**
```bash
git clone <repository-url>
cd chess_play_with_ai
```

2. **Run the setup script:**
```bash
python setup.py
```

The setup script will:
- Check Python version compatibility
- Create a virtual environment (if needed)
- Install all Python dependencies
- Check for Stockfish installation
- Run tests to verify everything works

### Manual Installation

1. **Clone and navigate:**
```bash
git clone <repository-url>
cd chess_play_with_ai
```

2. **Create virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

4. **Install Stockfish:**
   - **macOS**: `brew install stockfish`
   - **Ubuntu/Debian**: `sudo apt-get install stockfish`
   - **CentOS/RHEL**: `sudo yum install stockfish`
   - **Windows**: Download from [Stockfish website](https://stockfishchess.org/download/)

## 🎮 Usage

### Starting the Application

1. **Activate virtual environment** (if not already active):
```bash
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Start the server:**
```bash
python app.py
```

3. **Open your browser** and navigate to:
```
http://localhost:5001
```

### Playing Chess

1. **Start a New Game**: Click "New Game" and select your color and difficulty
2. **Make Moves**:
   - Click on a piece, then click the destination square
   - Or type moves in UCI notation (e.g., "e2e4") and press Enter
3. **Get Help**: Click "Get Suggestions" for AI move recommendations
4. **Switch Sides**: Use "Switch Colors" (only at game start)
5. **Reset**: Click "Reset" to restart the current game

## 📁 Project Structure

```
chess_play_with_ai/
├── app.py                    # Main Flask application server
├── config.py                 # Configuration settings
├── setup.py                  # Automated setup script
├── run_tests.py             # Test runner script
├── requirements.txt         # Python dependencies
├── chess_engine/            # Core chess logic package
│   ├── __init__.py
│   ├── game.py             # Chess game state management
│   ├── stockfish_ai.py     # Stockfish AI integration
│   └── utils.py            # Utility functions
├── static/                  # Frontend assets
│   ├── css/
│   │   └── style.css       # Main stylesheet
│   ├── js/
│   │   ├── chess.js        # Chess board visualization
│   │   └── app.js          # Main application logic
│   └── images/             # Image assets
├── templates/               # HTML templates
│   └── index.html          # Main game interface
├── tests/                   # Test suite
│   ├── __init__.py
│   ├── test_chess_engine.py # Chess engine tests
│   └── test_app.py         # Flask app tests
└── README.md               # This documentation
```

## 🔌 API Endpoints

### Game Management
- `GET /` - Main game interface (HTML)
- `POST /api/new_game` - Start a new game
- `POST /api/reset_game` - Reset current game
- `GET /api/game_state` - Get current game state

### Gameplay
- `POST /api/make_move` - Make a human move
- `POST /api/get_suggestions` - Get AI move suggestions
- `POST /api/switch_color` - Switch player color

### Information
- `GET /api/engine_info` - Get Stockfish engine information
- `GET /api/get_pgn` - Export game in PGN format

## ⚙️ Configuration

### Environment Variables
```bash
# Stockfish settings
STOCKFISH_PATH=/usr/local/bin/stockfish
AI_THINK_TIME=10.0
AI_DEPTH=15
AI_SUGGESTIONS_COUNT=3

# Flask settings
FLASK_DEBUG=True
SECRET_KEY=your-secret-key
```

### Difficulty Levels
- **Beginner**: 800 Elo - Good for learning
- **Intermediate**: 1200 Elo - Casual players
- **Advanced**: 1600 Elo - Club level
- **Expert**: 2000 Elo - Strong players
- **Master**: 2400 Elo - Master level
- **Grandmaster**: 3000 Elo - Maximum strength

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py
```

### Run Specific Tests
```bash
# Test chess engine only
python -m pytest tests/test_chess_engine.py -v

# Test Flask app only
python -m pytest tests/test_app.py -v

# Test with coverage
python -m pytest tests/ --cov=chess_engine --cov=app
```

## 🐛 Troubleshooting

### Common Issues

**1. Stockfish not found**
```bash
# Check if Stockfish is installed
stockfish --help

# Install on macOS
brew install stockfish

# Install on Ubuntu
sudo apt-get install stockfish
```

**2. Port already in use**
```bash
# Kill process using port 5000
lsof -ti:5000 | xargs kill -9

# Or run on different port
export FLASK_RUN_PORT=5001
python app.py
```

**3. Virtual environment issues**
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

**4. Permission errors on setup**
```bash
# Make scripts executable
chmod +x setup.py run_tests.py
```

### Performance Tuning

**For faster AI responses:**
- Reduce `AI_THINK_TIME` in config.py
- Lower `AI_DEPTH` for weaker but faster play
- Use lower difficulty levels

**For stronger AI play:**
- Increase `AI_THINK_TIME` (up to 30 seconds)
- Use higher difficulty levels
- Ensure sufficient system resources

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make changes and add tests
4. Run tests: `python run_tests.py`
5. Commit changes: `git commit -am 'Add feature'`
6. Push to branch: `git push origin feature-name`
7. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- **Stockfish Team** - For the incredible chess engine
- **python-chess** - For the excellent Python chess library
- **Flask** - For the lightweight web framework
