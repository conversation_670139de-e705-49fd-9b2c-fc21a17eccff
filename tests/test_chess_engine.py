"""Tests for chess engine components."""

import pytest
import chess
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_engine.game import Chess<PERSON>ame
from chess_engine.utils import move_to_dict, board_to_dict, validate_move_input
from chess_engine.stockfish_ai import StockfishAI


class TestChessGame:
    """Test cases for ChessGame class."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        with patch('chess_engine.stockfish_ai.Stockfish'):
            self.game = ChessGame(human_color='white', difficulty='intermediate')
    
    def test_initial_game_state(self):
        """Test initial game state is correct."""
        state = self.game.get_game_state()
        
        assert state['human_color'] == 'white'
        assert state['ai_color'] == 'black'
        assert state['turn'] == 'white'
        assert state['current_player'] == 'human'
        assert state['move_count'] == 0
        assert not state['is_game_over']
        assert state['fen'] == chess.Board().fen()
    
    def test_make_valid_human_move(self):
        """Test making a valid human move."""
        result = self.game.make_human_move('e2e4')
        
        assert result['success'] is True
        assert result['move']['uci'] == 'e2e4'
        assert len(self.game.move_history) == 1
        assert self.game.move_history[0]['player'] == 'human'
    
    def test_make_invalid_human_move(self):
        """Test making an invalid human move."""
        result = self.game.make_human_move('e2e5')  # Invalid move
        
        assert result['success'] is False
        assert 'Illegal move' in result['error']
        assert len(self.game.move_history) == 0
    
    def test_wrong_turn(self):
        """Test making a move when it's not the player's turn."""
        # Make a human move first
        self.game.make_human_move('e2e4')
        
        # Try to make another human move (should be AI's turn)
        result = self.game.make_human_move('d2d4')
        
        assert result['success'] is False
        assert "not your turn" in result['error']
    
    def test_switch_colors_at_start(self):
        """Test switching colors at the start of the game."""
        result = self.game.switch_human_color()
        
        assert result['success'] is True
        assert result['human_color'] == 'black'
        assert result['ai_color'] == 'white'
    
    def test_switch_colors_after_moves(self):
        """Test that switching colors fails after moves are made."""
        self.game.make_human_move('e2e4')
        result = self.game.switch_human_color()
        
        assert result['success'] is False
        assert "Cannot switch colors" in result['error']
    
    def test_reset_game(self):
        """Test resetting the game."""
        # Make some moves
        self.game.make_human_move('e2e4')
        
        # Reset the game
        result = self.game.reset_game()
        
        assert result['success'] is True
        assert len(self.game.move_history) == 0
        assert self.game.board.fen() == chess.Board().fen()
    
    def test_get_pgn(self):
        """Test PGN generation."""
        # Make a few moves
        self.game.make_human_move('e2e4')
        
        pgn = self.game.get_pgn()
        assert isinstance(pgn, str)
        assert len(pgn) > 0


class TestUtils:
    """Test cases for utility functions."""
    
    def test_move_to_dict(self):
        """Test move to dictionary conversion."""
        board = chess.Board()
        move = chess.Move.from_uci('e2e4')
        
        move_dict = move_to_dict(move, board)
        
        assert move_dict['from'] == 'e2'
        assert move_dict['to'] == 'e4'
        assert move_dict['uci'] == 'e2e4'
        assert move_dict['san'] == 'e4'
    
    def test_board_to_dict(self):
        """Test board to dictionary conversion."""
        board = chess.Board()
        
        board_dict = board_to_dict(board)
        
        assert board_dict['fen'] == board.fen()
        assert board_dict['turn'] == 'white'
        assert board_dict['is_check'] is False
        assert board_dict['is_checkmate'] is False
        assert board_dict['is_game_over'] is False
        assert len(board_dict['legal_moves']) == 20  # 20 legal moves in starting position
    
    def test_validate_move_input_valid(self):
        """Test move input validation with valid moves."""
        assert validate_move_input('e2e4') is True
        assert validate_move_input('a1h8') is True
        assert validate_move_input('e7e8q') is True  # Promotion
    
    def test_validate_move_input_invalid(self):
        """Test move input validation with invalid moves."""
        assert validate_move_input('e2') is False  # Too short
        assert validate_move_input('e2e4e5') is False  # Too long
        assert validate_move_input('z2e4') is False  # Invalid square
        assert validate_move_input('e2e9') is False  # Invalid square
        assert validate_move_input('e7e8x') is False  # Invalid promotion piece
        assert validate_move_input(123) is False  # Not a string


class TestStockfishAI:
    """Test cases for StockfishAI class."""
    
    @patch('chess_engine.stockfish_ai.Stockfish')
    def test_stockfish_initialization(self, mock_stockfish):
        """Test Stockfish AI initialization."""
        mock_instance = Mock()
        mock_stockfish.return_value = mock_instance
        
        ai = StockfishAI(difficulty='intermediate')
        
        assert ai.difficulty == 'intermediate'
        assert ai.think_time > 0
        mock_stockfish.assert_called_once()
    
    @patch('chess_engine.stockfish_ai.Stockfish')
    def test_set_difficulty(self, mock_stockfish):
        """Test setting AI difficulty."""
        mock_instance = Mock()
        mock_stockfish.return_value = mock_instance
        
        ai = StockfishAI()
        result = ai.set_difficulty('expert')
        
        assert result is True
        assert ai.difficulty == 'expert'
    
    @patch('chess_engine.stockfish_ai.Stockfish')
    def test_set_invalid_difficulty(self, mock_stockfish):
        """Test setting invalid AI difficulty."""
        mock_instance = Mock()
        mock_stockfish.return_value = mock_instance
        
        ai = StockfishAI()
        result = ai.set_difficulty('invalid')
        
        assert result is False
    
    @patch('chess_engine.stockfish_ai.Stockfish')
    def test_get_best_move(self, mock_stockfish):
        """Test getting best move from AI."""
        mock_instance = Mock()
        mock_instance.get_best_move_time.return_value = 'e2e4'
        mock_stockfish.return_value = mock_instance
        
        ai = StockfishAI()
        board = chess.Board()
        
        move = ai.get_best_move(board)
        
        assert move is not None
        assert move.uci() == 'e2e4'
    
    @patch('chess_engine.stockfish_ai.Stockfish')
    def test_get_move_suggestions(self, mock_stockfish):
        """Test getting move suggestions from AI."""
        mock_instance = Mock()
        mock_instance.get_top_moves.return_value = [
            {'Move': 'e2e4', 'Centipawn': 30, 'Mate': None},
            {'Move': 'd2d4', 'Centipawn': 25, 'Mate': None},
            {'Move': 'g1f3', 'Centipawn': 20, 'Mate': None}
        ]
        mock_stockfish.return_value = mock_instance
        
        ai = StockfishAI()
        board = chess.Board()
        
        suggestions = ai.get_move_suggestions(board, 3)
        
        assert len(suggestions) == 3
        assert suggestions[0]['move'] == 'e2e4'
        assert suggestions[0]['rank'] == 1
        assert 'evaluation' in suggestions[0]


if __name__ == '__main__':
    pytest.main([__file__])
