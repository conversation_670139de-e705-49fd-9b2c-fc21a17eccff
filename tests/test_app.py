"""Tests for Flask application."""

import pytest
import json
from unittest.mock import patch, Mock
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app


@pytest.fixture
def client():
    """Create a test client for the Flask app."""
    app.config['TESTING'] = True
    with app.test_client() as client:
        with app.app_context():
            yield client


@pytest.fixture
def mock_game():
    """Create a mock ChessGame instance."""
    with patch('app.ChessGame') as mock_chess_game:
        mock_instance = Mock()
        mock_chess_game.return_value = mock_instance
        
        # Set up default mock responses
        mock_instance.get_game_state.return_value = {
            'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
            'turn': 'white',
            'human_color': 'white',
            'ai_color': 'black',
            'current_player': 'human',
            'move_count': 0,
            'is_game_over': False,
            'legal_moves': ['e2e4', 'd2d4'],
            'result_description': None
        }
        
        mock_instance.make_human_move.return_value = {
            'success': True,
            'move': {'uci': 'e2e4', 'san': 'e4'},
            'game_state': mock_instance.get_game_state.return_value
        }
        
        mock_instance.make_ai_move.return_value = {
            'success': True,
            'move': {'uci': 'e7e5', 'san': 'e5'},
            'think_time': 2.5,
            'game_state': mock_instance.get_game_state.return_value
        }
        
        mock_instance.get_move_suggestions.return_value = [
            {'move': 'e2e4', 'san': 'e4', 'rank': 1, 'evaluation': '+0.30'},
            {'move': 'd2d4', 'san': 'd4', 'rank': 2, 'evaluation': '+0.25'},
            {'move': 'g1f3', 'san': 'Nf3', 'rank': 3, 'evaluation': '+0.20'}
        ]
        
        yield mock_instance


class TestAppRoutes:
    """Test cases for Flask application routes."""
    
    def test_index_route(self, client):
        """Test the main index route."""
        response = client.get('/')
        assert response.status_code == 200
        assert b'Chess AI' in response.data
    
    def test_new_game_route(self, client, mock_game):
        """Test creating a new game."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        response = client.post('/api/new_game', 
                             json={'human_color': 'white', 'difficulty': 'intermediate'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'game_state' in data
    
    def test_make_move_route_valid(self, client, mock_game):
        """Test making a valid move."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        # First create a game
        with patch('app.games', {'test_session': mock_game}):
            response = client.post('/api/make_move', json={'move': 'e2e4'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'human_move' in data
    
    def test_make_move_route_invalid(self, client, mock_game):
        """Test making an invalid move."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        # Set up mock to return failure
        mock_game.make_human_move.return_value = {
            'success': False,
            'error': 'Illegal move',
            'game_state': mock_game.get_game_state.return_value
        }
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.post('/api/make_move', json={'move': 'invalid'})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'error' in data
    
    def test_make_move_no_session(self, client):
        """Test making a move without an active session."""
        response = client.post('/api/make_move', json={'move': 'e2e4'})
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'No active game' in data['error']
    
    def test_get_game_state_route(self, client, mock_game):
        """Test getting game state."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.get('/api/game_state')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'game_state' in data
    
    def test_get_suggestions_route(self, client, mock_game):
        """Test getting move suggestions."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.post('/api/get_suggestions', json={'count': 3})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'suggestions' in data
        assert len(data['suggestions']) == 3
    
    def test_switch_color_route(self, client, mock_game):
        """Test switching player color."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        mock_game.switch_human_color.return_value = {
            'success': True,
            'human_color': 'black',
            'ai_color': 'white',
            'game_state': mock_game.get_game_state.return_value
        }
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.post('/api/switch_color')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['human_color'] == 'black'
    
    def test_reset_game_route(self, client, mock_game):
        """Test resetting the game."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        mock_game.reset_game.return_value = {
            'success': True,
            'game_state': mock_game.get_game_state.return_value
        }
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.post('/api/reset_game')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
    
    def test_get_pgn_route(self, client, mock_game):
        """Test getting PGN."""
        with client.session_transaction() as sess:
            sess['session_id'] = 'test_session'
        
        mock_game.get_pgn.return_value = '[Event "Chess AI Game"]\n1. e4 e5'
        
        with patch('app.games', {'test_session': mock_game}):
            response = client.get('/api/get_pgn')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'pgn' in data
    
    def test_engine_info_route(self, client):
        """Test getting engine info."""
        with patch('chess_engine.StockfishAI') as mock_ai:
            mock_instance = Mock()
            mock_instance.get_engine_info.return_value = {
                'path': '/usr/bin/stockfish',
                'difficulty': 'intermediate',
                'is_ready': True
            }
            mock_ai.return_value = mock_instance
            
            response = client.get('/api/engine_info')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'engine_info' in data
    
    def test_404_error_handler(self, client):
        """Test 404 error handling."""
        response = client.get('/api/nonexistent')
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'not found' in data['error']


class TestAppErrorHandling:
    """Test error handling in the application."""
    
    def test_missing_move_parameter(self, client):
        """Test making a move without providing the move parameter."""
        response = client.post('/api/make_move', json={})
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert 'Move is required' in data['error']
    
    def test_invalid_json(self, client):
        """Test sending invalid JSON."""
        response = client.post('/api/make_move', 
                             data='invalid json',
                             content_type='application/json')
        
        assert response.status_code == 400


if __name__ == '__main__':
    pytest.main([__file__])
