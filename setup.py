#!/usr/bin/env python3
"""
Setup script for Chess AI application.
This script helps with initial setup and dependency installation.
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version OK: {sys.version}")
    return True


def check_stockfish():
    """Check if Stockfish is installed and accessible."""
    system = platform.system().lower()
    
    # Common Stockfish paths
    if system == 'darwin':  # macOS
        paths = [
            '/opt/homebrew/bin/stockfish',
            '/usr/local/bin/stockfish',
            '/usr/bin/stockfish'
        ]
    elif system == 'linux':
        paths = [
            '/usr/bin/stockfish',
            '/usr/local/bin/stockfish',
            '/usr/games/stockfish'
        ]
    elif system == 'windows':
        paths = [
            'C:\\Program Files\\Stockfish\\stockfish.exe',
            'C:\\stockfish\\stockfish.exe'
        ]
    else:
        paths = ['stockfish']
    
    # Check if any path exists
    for path in paths:
        if os.path.exists(path):
            print(f"✅ Stockfish found at: {path}")
            return True
    
    # Try to run stockfish from PATH
    try:
        result = subprocess.run(['stockfish', '--help'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Stockfish found in PATH")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("❌ Stockfish not found!")
    print_stockfish_installation_instructions()
    return False


def print_stockfish_installation_instructions():
    """Print instructions for installing Stockfish."""
    system = platform.system().lower()
    
    print("\n📋 Stockfish Installation Instructions:")
    print("-" * 40)
    
    if system == 'darwin':  # macOS
        print("For macOS:")
        print("  brew install stockfish")
    elif system == 'linux':
        print("For Ubuntu/Debian:")
        print("  sudo apt-get update")
        print("  sudo apt-get install stockfish")
        print("\nFor CentOS/RHEL:")
        print("  sudo yum install stockfish")
    elif system == 'windows':
        print("For Windows:")
        print("  1. Download Stockfish from: https://stockfishchess.org/download/")
        print("  2. Extract to C:\\stockfish\\")
        print("  3. Or add stockfish.exe to your PATH")
    else:
        print("Please install Stockfish for your operating system.")
        print("Visit: https://stockfishchess.org/download/")


def install_python_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Python dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ pip not found. Please install pip first.")
        return False


def create_virtual_environment():
    """Create a virtual environment."""
    venv_path = 'venv'
    
    if os.path.exists(venv_path):
        print(f"✅ Virtual environment already exists at: {venv_path}")
        return True
    
    print(f"\n🔧 Creating virtual environment at: {venv_path}")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'venv', venv_path])
        print("✅ Virtual environment created successfully!")
        
        # Print activation instructions
        system = platform.system().lower()
        if system == 'windows':
            activate_cmd = f"{venv_path}\\Scripts\\activate"
        else:
            activate_cmd = f"source {venv_path}/bin/activate"
        
        print(f"\n📋 To activate the virtual environment, run:")
        print(f"   {activate_cmd}")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False


def run_tests():
    """Run the test suite."""
    print("\n🧪 Running tests...")
    
    try:
        result = subprocess.run([
            sys.executable, 'run_tests.py'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed.")
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Chess AI Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (
        hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
    )
    
    if not in_venv:
        print("\n⚠️  You're not in a virtual environment.")
        response = input("Create one? (y/n): ").lower().strip()
        if response == 'y':
            if not create_virtual_environment():
                sys.exit(1)
            print("\n⚠️  Please activate the virtual environment and run setup again.")
            sys.exit(0)
    else:
        print("✅ Virtual environment detected")
    
    # Install Python dependencies
    if not install_python_dependencies():
        sys.exit(1)
    
    # Check Stockfish
    stockfish_ok = check_stockfish()
    
    # Run tests
    if stockfish_ok:
        tests_ok = run_tests()
    else:
        print("\n⚠️  Skipping tests due to missing Stockfish")
        tests_ok = False
    
    # Final summary
    print("\n" + "=" * 50)
    print("🎯 Setup Summary:")
    print(f"   Python: ✅")
    print(f"   Dependencies: ✅")
    print(f"   Stockfish: {'✅' if stockfish_ok else '❌'}")
    print(f"   Tests: {'✅' if tests_ok else '❌'}")
    
    if stockfish_ok and tests_ok:
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Run: python app.py")
        print("   2. Open: http://localhost:5000")
        print("   3. Start playing chess!")
    else:
        print("\n⚠️  Setup completed with issues.")
        if not stockfish_ok:
            print("   Please install Stockfish to use the AI engine.")
        if not tests_ok:
            print("   Some tests failed - check the output above.")


if __name__ == '__main__':
    main()
