["tests/test_app.py::TestAppErrorHandling::test_invalid_json", "tests/test_app.py::TestAppErrorHandling::test_missing_move_parameter", "tests/test_app.py::TestAppRoutes::test_404_error_handler", "tests/test_app.py::TestAppRoutes::test_engine_info_route", "tests/test_app.py::TestAppRoutes::test_get_game_state_route", "tests/test_app.py::TestAppRoutes::test_get_pgn_route", "tests/test_app.py::TestAppRoutes::test_get_suggestions_route", "tests/test_app.py::TestAppRoutes::test_index_route", "tests/test_app.py::TestAppRoutes::test_make_move_no_session", "tests/test_app.py::TestAppRoutes::test_make_move_route_invalid", "tests/test_app.py::TestAppRoutes::test_make_move_route_valid", "tests/test_app.py::TestAppRoutes::test_new_game_route", "tests/test_app.py::TestAppRoutes::test_reset_game_route", "tests/test_app.py::TestAppRoutes::test_switch_color_route", "tests/test_chess_engine.py::TestChessGame::test_get_pgn", "tests/test_chess_engine.py::TestChessGame::test_initial_game_state", "tests/test_chess_engine.py::TestChessGame::test_make_invalid_human_move", "tests/test_chess_engine.py::TestChessGame::test_make_valid_human_move", "tests/test_chess_engine.py::TestChessGame::test_reset_game", "tests/test_chess_engine.py::TestChessGame::test_switch_colors_after_moves", "tests/test_chess_engine.py::TestChessGame::test_switch_colors_at_start", "tests/test_chess_engine.py::TestChessGame::test_wrong_turn", "tests/test_chess_engine.py::TestStockfishAI::test_get_best_move", "tests/test_chess_engine.py::TestStockfishAI::test_get_move_suggestions", "tests/test_chess_engine.py::TestStockfishAI::test_set_difficulty", "tests/test_chess_engine.py::TestStockfishAI::test_set_invalid_difficulty", "tests/test_chess_engine.py::TestStockfishAI::test_stockfish_initialization", "tests/test_chess_engine.py::TestUtils::test_board_to_dict", "tests/test_chess_engine.py::TestUtils::test_move_to_dict", "tests/test_chess_engine.py::TestUtils::test_validate_move_input_invalid", "tests/test_chess_engine.py::TestUtils::test_validate_move_input_valid"]