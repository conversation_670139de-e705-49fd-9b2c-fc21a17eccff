#!/usr/bin/env python3
"""
Test runner script for Chess AI application.
"""

import sys
import subprocess
import os


def run_tests():
    """Run all tests and display results."""
    print("🧪 Running Chess AI Tests")
    print("=" * 50)
    
    # Change to the project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    # Run pytest with verbose output
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'tests/', 
            '-v',
            '--tb=short',
            '--color=yes'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("\n✅ All tests passed!")
        else:
            print(f"\n❌ Tests failed with return code: {result.returncode}")
            
        return result.returncode
        
    except FileNotFoundError:
        print("❌ pytest not found. Please install it with: pip install pytest")
        return 1
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = {
        'pytest': 'pytest',
        'flask': 'flask',
        'python-chess': 'chess',  # python-chess imports as 'chess'
        'stockfish': 'stockfish'
    }
    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall them with: pip install -r requirements.txt")
        return False

    return True


if __name__ == '__main__':
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    print("✅ Dependencies OK\n")
    
    exit_code = run_tests()
    sys.exit(exit_code)
