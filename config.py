"""Configuration settings for the Chess AI application."""

import os
import platform


def _get_default_stockfish_path():
    """Get the default Stockfish path based on the operating system."""
    system = platform.system().lower()

    if system == 'darwin':  # macOS
        # Common paths for Homebrew installation
        paths = [
            '/opt/homebrew/bin/stockfish',  # Apple Silicon
            '/usr/local/bin/stockfish',     # Intel Mac
            '/usr/bin/stockfish'
        ]
    elif system == 'linux':
        paths = [
            '/usr/bin/stockfish',
            '/usr/local/bin/stockfish',
            '/usr/games/stockfish'
        ]
    elif system == 'windows':
        paths = [
            'C:\\Program Files\\Stockfish\\stockfish.exe',
            'C:\\stockfish\\stockfish.exe',
            'stockfish.exe'  # If in PATH
        ]
    else:
        paths = ['stockfish']  # Fallback

    # Return the first existing path
    for path in paths:
        if os.path.exists(path) or (system == 'windows' and path == 'stockfish.exe'):
            return path

    # If no path found, return the first one (will need manual configuration)
    return paths[0] if paths else 'stockfish'


class Config:
    """Application configuration."""

    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'chess-ai-secret-key-2024'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    # Stockfish settings
    STOCKFISH_PATH = os.environ.get('STOCKFISH_PATH') or _get_default_stockfish_path()
    
    # AI settings
    AI_THINK_TIME = float(os.environ.get('AI_THINK_TIME', '10.0'))  # seconds
    AI_DEPTH = int(os.environ.get('AI_DEPTH', '15'))  # search depth
    AI_SUGGESTIONS_COUNT = int(os.environ.get('AI_SUGGESTIONS_COUNT', '3'))
    
    # Difficulty levels (Elo ratings)
    DIFFICULTY_LEVELS = {
        'beginner': 800,
        'intermediate': 1200,
        'advanced': 1600,
        'expert': 2000,
        'master': 2400,
        'grandmaster': 3000
    }

    DEFAULT_DIFFICULTY = 'intermediate'
