<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chess AI - Play Against Stockfish</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-chess"></i> Chess AI</h1>
            <p>Play against the powerful Stockfish engine</p>
        </header>

        <div class="game-container">
            <!-- Game Controls -->
            <div class="controls-panel">
                <div class="game-info">
                    <div class="player-info">
                        <div class="player human-player">
                            <i class="fas fa-user"></i>
                            <span id="human-color">White</span>
                            <span class="player-label">Human</span>
                        </div>
                        <div class="vs">VS</div>
                        <div class="player ai-player">
                            <i class="fas fa-robot"></i>
                            <span id="ai-color">Black</span>
                            <span class="player-label">AI</span>
                        </div>
                    </div>
                    
                    <div class="game-status">
                        <div class="turn-indicator">
                            <span id="current-turn">White to move</span>
                        </div>
                        <div class="game-result" id="game-result" style="display: none;"></div>
                    </div>
                </div>

                <div class="control-buttons">
                    <button id="new-game-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Game
                    </button>
                    <button id="switch-color-btn" class="btn btn-secondary">
                        <i class="fas fa-exchange-alt"></i> Switch Colors
                    </button>
                    <button id="get-suggestions-btn" class="btn btn-info">
                        <i class="fas fa-lightbulb"></i> Get Suggestions
                    </button>
                    <button id="reset-game-btn" class="btn btn-warning">
                        <i class="fas fa-redo"></i> Reset
                    </button>
                </div>

                <div class="difficulty-selector">
                    <label for="difficulty">AI Difficulty:</label>
                    <select id="difficulty" class="form-control">
                        <option value="beginner">Beginner (800)</option>
                        <option value="intermediate" selected>Intermediate (1200)</option>
                        <option value="advanced">Advanced (1600)</option>
                        <option value="expert">Expert (2000)</option>
                        <option value="master">Master (2400)</option>
                        <option value="grandmaster">Grandmaster (3000)</option>
                    </select>
                </div>
            </div>

            <!-- Chess Board -->
            <div class="board-container">
                <div id="chess-board" class="chess-board">
                    <!-- Board will be generated by JavaScript -->
                </div>
                
                <div class="move-input">
                    <label for="move-input">Enter move (e.g., e2e4):</label>
                    <div class="input-group">
                        <input type="text" id="move-input" placeholder="e2e4" maxlength="5">
                        <button id="make-move-btn" class="btn btn-success">
                            <i class="fas fa-play"></i> Move
                        </button>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="side-panel">
                <!-- Move Suggestions -->
                <div class="suggestions-panel">
                    <h3><i class="fas fa-lightbulb"></i> Move Suggestions</h3>
                    <div id="suggestions-list" class="suggestions-list">
                        <p class="no-suggestions">Click "Get Suggestions" to see AI recommendations</p>
                    </div>
                </div>

                <!-- Move History -->
                <div class="history-panel">
                    <h3><i class="fas fa-history"></i> Move History</h3>
                    <div id="move-history" class="move-history">
                        <p class="no-moves">No moves yet</p>
                    </div>
                </div>

                <!-- Game Info -->
                <div class="info-panel">
                    <h3><i class="fas fa-info-circle"></i> Game Info</h3>
                    <div id="game-info" class="game-info-details">
                        <div class="info-item">
                            <span class="label">Moves:</span>
                            <span id="move-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Duration:</span>
                            <span id="game-duration">0:00</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Engine:</span>
                            <span id="engine-status">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <i class="fas fa-chess-knight fa-spin"></i>
                <p id="loading-text">AI is thinking...</p>
            </div>
        </div>

        <!-- Modal for New Game -->
        <div id="new-game-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3>Start New Game</h3>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Play as:</label>
                        <div class="radio-group">
                            <label>
                                <input type="radio" name="color" value="white" checked>
                                <i class="fas fa-chess-king" style="color: #fff;"></i> White
                            </label>
                            <label>
                                <input type="radio" name="color" value="black">
                                <i class="fas fa-chess-king" style="color: #333;"></i> Black
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="modal-difficulty">Difficulty:</label>
                        <select id="modal-difficulty" class="form-control">
                            <option value="beginner">Beginner (800)</option>
                            <option value="intermediate" selected>Intermediate (1200)</option>
                            <option value="advanced">Advanced (1600)</option>
                            <option value="expert">Expert (2000)</option>
                            <option value="master">Master (2400)</option>
                            <option value="grandmaster">Grandmaster (3000)</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="cancel-new-game" class="btn btn-secondary">Cancel</button>
                    <button id="confirm-new-game" class="btn btn-primary">Start Game</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/chess.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
